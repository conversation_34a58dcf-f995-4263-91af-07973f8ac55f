[agent]
  interval = "30s"
  round_interval = true
  metric_batch_size = 100
  metric_buffer_limit = 1000
  collection_jitter = "5s"
  flush_interval = "30s"
  flush_jitter = "5s"
  precision = ""
  hostname = ""
  omit_hostname = false
  debug = false
  quiet = true
  logtarget = "file"
  logfile = "/var/log/telegraf/telegraf.log"
  logfile_rotation_interval = "24h"
  logfile_rotation_max_size = "10MB"
  logfile_rotation_max_archives = 3

[[inputs.cpu]]
  percpu = false
  totalcpu = true
  collect_cpu_time = false
  report_active = true
  interval = "60s"

[[inputs.mem]]
  interval = "60s"

[[inputs.disk]]
  interval = "120s"

[[inputs.diskio]]
  skip_serial_number = true
  interval = "120s"

[[inputs.system]]
  interval = "60s"

[[inputs.net]]
  interval = "60s"

[[inputs.chrony]]
  metrics = ["tracking"]
  interval = "30s"

# Graphite output (deprecated - to be removed)
[[outputs.graphite]]
  servers = ["localhost:2003"]
  prefix = "i2r"
  template = "host.tags.measurement.field"
  timeout = "5s"
  batch_size = 100

# InfluxDB output (without a real InfluxDB server)
[[outputs.file]]
  files = ["/var/lib/i2r/metrics.log"]
  data_format = "influx"
  rotation_max_size = "10MB"
  rotation_max_archives = 5
  flush_interval = "30s"